public class Main {
    public static void main(String[] args) {
        boolean[][] matrix = new boolean[5][4];
        for (int i = 0; i < 5; i++) {
            for (int j = 0; j < 4; j++) {
                matrix[i][j] = true;
            }
        }
        new Solution().flip(matrix, 0, 0, 5, 4);
        System.out.println(new Solution().allFalse(matrix, 5, 4) + " true");
        

        int a;
        
        a = new Solution().solve(0, 0, new boolean[][] {});
        System.out.println(a + " 0");
        
        a = new Solution().solve(1, 1, new boolean[][] {
            new boolean[] { false },
        });
        System.out.println(a + " 0");
        
        a = new Solution().solve(1, 1, new boolean[][] {
            new boolean[] { true },
        });
        System.out.println(a + " 1");
        
        a = new Solution().solve(2, 2, new boolean[][] {
            new boolean[] { false, true },
            new boolean[] { false, true },
        });
        System.out.println(a + " 1");
        
        a = new Solution().solve(3, 2, new boolean[][] {
            new boolean[] { false, true },
            new boolean[] { true, true },
            new boolean[] { false, true },
        });
        System.out.println(a + " 2");
        
        a = new Solution().solve(5, 2, new boolean[][] {
            new boolean[] { true, false },
            new boolean[] { false, true },
            new boolean[] { true, false },
            new boolean[] { false, true },
            new boolean[] { true, false },
        });
        System.out.println(a + " 5");
        
        a = new Solution().solve(8, 5, new boolean[][] {
            new boolean[] { false, true, true, true, true },
            new boolean[] { false, true, true, true, true },
            new boolean[] { false, true, true, true, true },
            new boolean[] { false, true, true, true, true },
            new boolean[] { false, true, true, true, true },
            new boolean[] { false, true, false, true, true },
            new boolean[] { false, true, false, true, true },
            new boolean[] { false, false, true, true, true },
        });
        System.out.println(a + " 3");
    }
}