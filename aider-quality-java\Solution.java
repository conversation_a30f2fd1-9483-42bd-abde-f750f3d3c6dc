import java.util.*;

class Solution {
    public void flip(boolean[][] matrix, int y0, int x0, int h0, int w0) {
        for (int y = y0; y < y0 + h0; y++) {
            for (int x = x0; x < x0 + w0; x++) {
                matrix[y][x] = !matrix[y][x];
            }
        }
    }

    public boolean allFalse(boolean[][] matrix, int m, int n) {
        for (int y = 0; y < m; y++) {
            for (int x = 0; x < n; x++) {
                if (matrix[y][x]) {
                    return false;
                }
            }
        }

        return true;
    }

    private int countTrues(boolean[][] matrix, int m, int n) {
        int count = 0;
        for (int y = 0; y < m; y++) {
            for (int x = 0; x < n; x++) {
                if (matrix[y][x]) {
                    count++;
                }
            }
        }

        return count;
    }

    private int solveSub(int m, int n, int y0, int x0, int h0, int w0, boolean[][] matrix, int nCurRects, int curMin) {
        if (allFalse(matrix, m, n)) {
            return nCurRects;
        }

        if (nCurRects >= curMin - 1) {
            return -1;
        }


        int foundMin = -1;


        for (int h = h0; h <= m; h++) {
            for (int w = w0; w <= n; w++) {
                for (int y = y0; y <= m - h; y++) {
                    for (int x = x0; x <= n - w; x++) {
                        if (h == h0 && w == w0 && y == y0 && x == x0) {
                            continue;
                        }

                        flip(matrix, y, x, h, w);
                        int foundCount = solveSub(m, n, y, x, h, w, matrix, nCurRects + 1, curMin);
                        flip(matrix, y, x, h, w);

                        if (foundCount == -1) {
                            continue;
                        }

                        foundMin = foundCount;
                    }
                }
            }
        }

        return foundMin;
    }

    public int solve(int m, int n, boolean[][] matrix) {
        if (allFalse(matrix, m, n)) {
            return 0;
        }

        int curMin = countTrues(matrix, m, n);

        if (0 >= curMin - 1 {
            return curMin;
        }

        for (int h = 1; h <= m; h++) {
            for (int w = 1; w <= n; w++) {
                for (int y = 0; y <= m - h; y++) {
                    for (int x = 0; x <= n - w; x++) {
                        flip(matrix, y, x, h, w);
                        int foundCount = solveSub(m, n, y, x, h, w, matrix, 1, curMin);
                        flip(matrix, y, x, h, w);

                        if (foundCount == -1) {
                            continue;
                        }

                        curMin = foundCount;
                    }
                }
            }
        }

        return curMin;
    }
}